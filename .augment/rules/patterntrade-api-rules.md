---
type: 'always_apply'
---

# PatternTrade API Development Rules

This document codifies all project-specific coding standards, architectural guidelines, technology preferences, and development patterns for the PatternTrade API project. This serves as the definitive reference for maintaining consistency across the codebase.

## Technology Stack & Core Dependencies

### Primary Technologies

- **Runtime**: Node.js 22+
- **Language**: TypeScript (ES2023 target, strict mode enabled)
- **Framework**: NestJS with microservice architecture
- **Package Manager**: pnpm (mandatory - never use npm/yarn)
- **Database**: PostgreSQL with Drizzle ORM
- **Time-series Database**: QuestDB for market data
- **Cache/Session Store**: Redis (DragonflyDB implementation)
- **Microservice Communication**: Redis transport for all inter-service communication
- **Validation**: Zod v4 (comprehensive schema validation)
- **Logging**: Pino (structured logging)
- **Testing**: Jest with ts-jest
- **Containerization**: Docker with Tilt for development orchestration

### Essential Libraries

- `nestjs-zod` - Validation pipes and decorators
- `nestjs-pino` - Structured logging integration
- `nestjs-cls` - Context management across async operations
- `drizzle-orm` - Type-safe database operations
- `dayjs` - Date/time manipulation (with UTC plugin)
- `helmet` - Security headers middleware
- `zod/v4` - Schema validation and type generation
- `kiteconnect` - Zerodha Kite Connect library for broker integration

## Project Architecture & Structure

### Monorepo Organization

```
patterntrade-api/
├── apps/                    # Microservice applications
│   ├── api/                # Main API gateway service
│   ├── analyser/           # Market analysis and strategy service
│   ├── oms/                # Order Management System
│   ├── simulator/          # Paper trading simulator
│   └── ticker/             # Real-time market data service
├── libs/                   # Shared libraries and modules
│   ├── core/               # Core infrastructure (DB, cache, session)
│   ├── common/             # Shared utilities, constants, schemas
│   ├── utils/              # Helper functions and utilities
│   ├── broker/             # Broker integration abstractions
│   └── shared/             # Database schemas and types
├── .augment/               # Development rules and configurations
├── drizzle/                # Database migrations and schema
└── docs/                   # Project documentation
```

### Library Responsibilities

- **Core Library**: Infrastructure services (database, cache, session, logging, environment)
- **Common Library**: Shared constants, schemas, errors, filters, base classes
- **Utils Library**: Helper functions, validation utilities, naming conventions
- **Broker Library**: Zerodha Kite Connect integration and trading operations

### Application Boundaries

- **API**: HTTP gateway, authentication, request routing
- **Analyser**: Technical analysis, signal generation
- **OMS**: Order placement, execution, position management
- **Simulator**: Paper trading, strategy backtesting, strategy testing without real money
- **Ticker**: Market data ingestion, real-time data streaming

### Stateless Application Architecture

- **Ensure the application remains completely stateless** by never storing data in memory (variables, caches, or application state)
- All data must be persisted to external stores (database, Redis, etc.) to maintain horizontal scalability and enable proper microservice architecture
- Use Redis for session management, caching, and temporary data storage
- Design services to be horizontally scalable without memory dependencies

## TypeScript & Code Standards

### Type Safety Rules (Mandatory)

- **NEVER use `any` type anywhere in the codebase**
- Use `unknown` type when type is uncertain, then narrow with type guards
- Prefer specific types with proper Zod schemas
- Use `type` imports for type-only imports: `import type { Type } from 'module'`
- Export TypeScript types from Zod schemas instead of `z.output<typeof Schema>`

### Enum and Constant Patterns

- **Prefer TypeScript enums over hardcoded string literals** for better IntelliSense
- **Always reference enum variables instead of hardcoded enum string values** - use `BrokerTypeEnum.enum.ZERODHA` instead of `'ZERODHA'` to maintain type safety and enable better refactoring support
- Use Zod enums for validation and type safety combined
- Replace runtime mapping methods with direct Zod enum usage
- Define constants in dedicated `*.constants.ts` files
- Use `as const` assertions for immutable objects

### Async/Sync Method Patterns

- **Avoid Promise.resolve/reject in synchronous methods**
- Use direct `return value` and `throw new Error()` instead of wrapping in promises
- Remove `await` from calls to methods changed from async to sync
- Mark methods as `async` only when they perform actual async operations

### Date/Time Handling Standard

- **Never use JavaScript's native `Date` object directly** in application code
- Always use the `datetimeutils` methods from the utils library to ensure consistent timezone handling, formatting, and date operations across the codebase
- Use dayjs with UTC plugin for all date/time operations
- Maintain consistent date formatting and timezone handling

### Environment Variable Access

- **NEVER use `process.env` directly** - always use `EnvService`
- Validate all environment variables with Zod schemas
- Separate environment validation from dynamic configuration schemas
- Support multiple deployment environments (local, docker, staging, production)

## File Naming & Organization Conventions

### File Naming Patterns

- **General files**: kebab-case (`user-service.ts`, `market-data.controller.ts`)
- **Classes**: PascalCase (`UserService`, `MarketDataController`)
- **Variables/functions**: camelCase (`getUserData`, `calculateMovingAverage`)
- **Constants**: SCREAMING_SNAKE_CASE (`MAX_RETRY_ATTEMPTS`, `DEFAULT_TIMEOUT`)

### Specific File Type Suffixes

- **Services**: `*.service.ts` (business logic)
- **Controllers**: `*.controller.ts` (HTTP endpoints)
- **Modules**: `*.module.ts` (NestJS modules)
- **Schemas**: `*.schema.ts` (Zod validation schemas)
- **Errors**: `*.error.ts` (custom error classes)
- **Constants**: `*.constants.ts` (configuration constants)
- **Interfaces**: `*.interface.ts` (TypeScript interfaces)
- **Types**: `*.types.ts` (type definitions)
- **Tests**: `*.spec.ts` (unit tests)
- **E2E Tests**: `*.e2e-spec.ts` (end-to-end tests)

### Index File Strategy

- Use `index.ts` for clean public API exports
- Export all public APIs from library root
- Group related exports logically
- Avoid deep import paths in consuming code

### Import/Export Organization

```typescript
// 1. Node.js built-ins
import { readFile } from 'fs/promises';

// 2. Third-party libraries
import { Injectable, Logger } from '@nestjs/common';
import { z } from 'zod/v4';

// 3. Internal libraries (path aliases)
import { EnvService } from '@app/core/env';
import { BrokerTypeEnum } from '@app/common/constants';

// 4. Relative imports
import { LocalService } from './local.service';

// 5. Type-only imports (last)
import type { User } from './user.types';
```

## NestJS Architecture Patterns

### Module Structure Guidelines

- Use `@Global()` decorator only for core infrastructure modules
- Import dependencies explicitly in each module (avoid implicit dependencies)
- Export services that other modules need to consume
- Keep modules focused and cohesive (single responsibility)
- Use feature modules for domain-specific functionality

### Dependency Injection Patterns

```typescript
// Constructor injection (preferred)
@Injectable()
export class ExampleService {
  private readonly logger = new Logger(ExampleService.name);

  constructor(
    private readonly envService: EnvService,
    private readonly drizzleService: DrizzleService,
  ) {}
}

// Factory providers for complex setups
export const ComplexServiceProvider: FactoryProvider = {
  provide: 'COMPLEX_SERVICE',
  useFactory: (envService: EnvService) => {
    return new ComplexService(envService.get('CONFIG_VALUE'));
  },
  inject: [EnvService],
};

// Custom decorators for common injections
export function ApiClient() {
  return Inject(constant.API_CLIENT);
}
```

### Service Implementation Patterns

- Include proper logging with context
- Implement comprehensive error handling
- Use dependency injection for all external dependencies
- Follow single responsibility principle
- Include proper TypeScript typing

## Database & Data Access Patterns

### Drizzle ORM Standards

- Use Drizzle for all PostgreSQL operations
- Implement base repository pattern for common CRUD operations
- Use Drizzle connection pooling for all database operations
- Implement Drizzle transactions for multi-step database operations to ensure data consistency and optimal resource utilization
- Include audit fields (createdAt, updatedAt, createdBy, updatedBy)
- Implement proper error handling with domain-specific errors

### QuestDB Time-Series Patterns

- Use QuestDB for high-frequency time-series data (ticks, OHLC, etc.)
- Implement proper connection pooling and management
- Use batch inserts for high-volume data ingestion
- Include proper error handling and reconnection logic
- Optimize queries for time-based operations

### Schema Organization

```typescript
// Base schema pattern
export const baseEntitySchema = z
  .object({
    id: z.number().int().positive().describe('Unique identifier'),
  })
  .merge(createdAuditSchema);

// Domain-specific schema
export const userSchema = baseEntitySchema.extend({
  email: emailSchema,
  name: z.string().min(1).max(255),
  status: UserStatusEnum,
});

// Export types
export type User = z.output<typeof userSchema>;
```

### Repository Pattern Implementation

- Extend `BaseRepository` for common operations
- Implement domain-specific methods
- Use proper error handling and logging
- Include pagination support
- Implement soft delete patterns where appropriate

## Validation & Schema Patterns

### Zod Schema Standards

- Define all schemas in `*.schema.ts` files
- Use consistent naming: `EntitySchema`, `CreateEntitySchema`, `UpdateEntitySchema`
- Export TypeScript types from schemas (not `z.output`)
- Include detailed descriptions for all fields
- Use base schemas for common patterns

### Validation Utilities

```typescript
// Safe parsing with error handling
export function safeParse<T extends z.ZodTypeAny>(
  schema: T,
  data: unknown,
  options?: { domain?: string; context?: string },
): { success: true; data: z.output<T> } | { success: false; error: ZodError };

// Validation pipes for controllers
export function createZodValidationPipe<T extends z.ZodTypeAny>(
  schema: T,
  options?: { domain?: string; context?: string },
);
```

### Response Schema Patterns

```typescript
// Standard API responses
export const successResponseSchema = z.object({
  success: z.literal(true),
  data: z.unknown(),
  timestamp: utcDateTimeSchema,
});

export const errorResponseSchema = z.object({
  success: z.literal(false),
  error: z.string(),
  timestamp: utcDateTimeSchema,
  statusCode: z.number().int(),
});

// Paginated responses
export const paginatedResponseSchema = <T extends z.ZodTypeAny>(dataSchema: T) =>
  z.object({
    success: z.literal(true),
    data: z.array(dataSchema),
    meta: paginationMetaSchema,
    timestamp: utcDateTimeSchema,
  });
```

## Error Handling Architecture

### Error Hierarchy

```typescript
// Base error class
export class BaseError<T extends string> extends Error {
  name: T;
  domain: string;
  message: string;
  cause?: unknown;
}

// Domain-specific error classes
export class BrokerError extends BaseError<BrokerErrorEnumType> {}
export class ZodError extends BaseError<ZodErrorEnumType> {}
export class AuthError extends BaseError<AuthErrorEnumType> {}
```

### Error Handling Patterns

- Use domain-specific error classes for different error types
- Include detailed error context and metadata
- Implement proper error filtering in global exception filter
- Log errors with structured data and appropriate levels
- Return user-friendly error messages in API responses

### Validation Error Enhancement

- Use Zod for comprehensive input validation
- Implement enhanced error formatting with field-level details
- Include validation context and expected formats
- Provide actionable error messages for API consumers

## Zerodha Kite Integration Patterns

### Single Broker Architecture (Scope Reduction)

- **Focus**: Direct integration with Zerodha Kite Connect library only
- **Rationale**: Accelerate time-to-market for live trading capabilities
- **Future-proofing**: Maintain clean code structure for potential broker expansion
- Use existing KiteConnect library for all broker operations
- Implement robust WebSocket connection management with reconnection logic
- Include comprehensive error handling and retry mechanisms specific to Kite API

### Zerodha Kite Symbol Management

- Use Zerodha Kite native symbol formats directly (no cross-broker mapping needed)
- Implement Kite-specific symbol validation and normalization
- Cache Kite instrument master data with appropriate TTL
- Support Kite exchanges: NSE, BSE, NFO, BFO, CDS, MCX
- Use Kite instrument tokens for efficient symbol identification

### Kite Market Data Handling

```typescript
// Kite subscription modes (direct mapping)
export const KiteSubscriptionModeEnum = z.enum(['ltp', 'quote', 'full']);

// Kite WebSocket message patterns
export const KITE_WS_MESSAGE_TYPES = {
  CONNECT: 'connect',
  SUBSCRIBE: 'subscribe',
  UNSUBSCRIBE: 'unsubscribe',
  TICK: 'tick',
  ERROR: 'error',
  RECONNECT: 'reconnect',
} as const;

// Kite API configuration
export const KITE_CONFIG = {
  name: 'Zerodha Kite',
  baseUrl: 'https://api.kite.trade',
  websocketUrl: 'wss://ws.kite.trade',
  limits: {
    maxOrdersPerSecond: 10,
    maxSubscriptions: 3000,
    maxHistoricalDays: 2000,
    connectionTimeout: 30000,
    requestTimeout: 10000,
  },
  exchanges: ['NSE', 'BSE', 'NFO', 'BFO', 'CDS', 'MCX'],
} as const;
```

### Kite Connect Integration Standards

- Use official `kiteconnect` npm package for all API interactions
- Implement proper session management for Kite access tokens
- Handle Kite-specific error codes and rate limiting
- Use Kite instrument tokens for symbol identification
- Implement Kite WebSocket ticker for real-time data

## Testing Standards & Patterns

### Test Organization

- **Unit tests**: `*.spec.ts` (test individual components)
- **E2E tests**: `*.e2e-spec.ts` (test complete workflows)
- **Test utilities**: Dedicated helper files for common test patterns
- **Mock implementations**: For external services and dependencies

### Jest Configuration Standards

```json
{
  "testRegex": ".*\\.spec\\.ts$",
  "testEnvironment": "node",
  "collectCoverageFrom": ["**/*.(t|j)s"],
  "coverageDirectory": "./coverage",
  "moduleNameMapper": {
    "^@app/core(|/.*)$": "<rootDir>/libs/core/src/$1",
    "^@app/common(|/.*)$": "<rootDir>/libs/common/src/$1"
  }
}
```

### Testing Best Practices

- Use descriptive test names that explain the scenario
- Group related tests with `describe` blocks
- Use `beforeEach`/`afterEach` for proper setup and cleanup
- Mock external dependencies and services
- Test both success and error conditions
- Include integration tests for critical workflows

### Test File Quality Standards

- Do not fix build errors, ESLint violations, or TypeScript type issues in test files (`*.spec.ts`, `*.e2e-spec.ts`) during regular development tasks
- Test files should be addressed separately to maintain focus on production code quality
- Keep test file maintenance as a dedicated activity separate from feature development

### Test Database Management

- Use separate test database configuration
- Reset database state between test suites
- Use transactions for test isolation
- Include proper test data setup and teardown
- Mock time-dependent operations

## Development Workflow Standards

### Package Management Rules

- **Always use pnpm** for all package operations
- Never manually edit package.json for dependencies
- Use `pnpm add/remove` for dependency management
- Keep pnpm-lock.yaml committed and up-to-date
- Use `--frozen-lockfile` in CI/CD environments

### Code Quality Standards

```bash
# Linting and formatting
pnpm run lint:fix          # Fix linting and formatting issues
pnpm run format:check      # Check formatting without changes
pnpm run test              # Run unit tests
pnpm run test:cov          # Run tests with coverage
```

### Git Workflow Patterns

- **Branch naming**: `feature/issue-{number}-{brief-description}`
- **Commit messages**: Use conventional commit format
- **Pull requests**: Target `dev` branch, require code review
- **Atomic commits**: Keep commits focused and self-contained

### Development Environment

```bash
# Development commands
pnpm run start:dev         # Start with hot reload
pnpm run start:debug       # Start with debugging enabled
pnpm run db:studio         # Open Drizzle Studio
pnpm run db:migrate        # Run database migrations
```

## Security & Performance Guidelines

### Security Standards

- Use JWT tokens for authentication with proper expiration
- Implement comprehensive session management
- Include CORS configuration for cross-origin requests
- Use Helmet middleware for security headers
- Validate all inputs with Zod schemas
- Log security events and suspicious activities

### Performance Optimization

- Implement database connection pooling
- Use appropriate caching strategies with Redis
- Include proper database indexes
- Monitor query performance and optimize slow queries
- Use streaming for large dataset operations
- Implement request rate limiting

### Monitoring & Observability

- Use structured logging with Pino
- Include request context in all logs
- Implement health check endpoints
- Monitor application metrics and performance
- Use distributed tracing for microservice communication
- Alert on error rates and performance degradation

## Trading-Specific Business Rules

### Order Management Patterns

- Validate all orders before submission to brokers
- Include comprehensive audit trails for all trading actions
- Support multiple order types (MARKET, LIMIT, SL, SL-M)
- Implement order status tracking and updates
- Include position and risk validation

### Risk Management Implementation

- Validate position limits before order placement
- Check account balances and margin requirements
- Implement circuit breakers for unusual activity
- Log all trading decisions with context
- Include real-time risk monitoring

### Market Data Standards

- Use subscription-based patterns for real-time data
- Implement proper data validation and normalization
- Support multiple subscription modes (LTP, QUOTE, FULL)
- Include rate limiting and throttling for API calls
- Cache frequently accessed market data

### Kite Symbol Universe Management

- Use Kite native symbol formats and instrument tokens directly
- Implement Kite-specific symbol validation using instrument master
- Support Kite instrument types: EQ, FUT, CE, PE, INDEX
- Use Kite exchange and segment classifications
- Validate symbols against Kite instrument master before trading operations
- Cache Kite instrument master data for offline symbol validation

## Code Formatting & Style

### Prettier Configuration

```json
{
  "singleQuote": true,
  "trailingComma": "all",
  "printWidth": 120,
  "tabWidth": 2,
  "semi": true,
  "bracketSpacing": true,
  "arrowParens": "always",
  "endOfLine": "auto"
}
```

### ESLint Rules

- Enforce `@typescript-eslint/no-explicit-any`: error
- Use `@typescript-eslint/consistent-type-imports`: error
- Prefer `@typescript-eslint/no-unused-vars`: warn
- Disable unsafe TypeScript rules for flexibility
- Enforce proper import organization

### Documentation Standards

- Use JSDoc for all public APIs
- Include parameter and return type descriptions
- Provide usage examples for complex functions
- Document error conditions and edge cases
- Maintain up-to-date README files

## Deployment & Infrastructure

### Docker Standards

- Use multi-stage builds for production
- Include proper health checks
- Use non-root users for security
- Optimize image layers for caching
- Include proper environment configuration

### Environment Management

- Support multiple deployment environments
- Use environment-specific configuration files
- Validate all environment variables on startup
- Include proper secrets management
- Use feature flags for gradual rollouts

### Monitoring Requirements

- Implement comprehensive health checks
- Include application metrics and monitoring
- Use structured logging for observability
- Monitor database and external service health
- Alert on critical system failures

---

_This document serves as the authoritative reference for PatternTrade API development. It should be consulted for all development decisions and updated as new patterns emerge or requirements change._

## Specification-Driven Development

### Kiro Specification Integration

- Examine spec files in `.kiro/specs/` directories before implementation
- Create structured task lists from defined specification tasks
- Implement systematically with proper task status updates
- Follow specification requirements precisely
- Suggest comprehensive tests after each implementation phase

### Development Process

1. **GitHub Issue Management**: Create or reference issues for all development tasks
2. **Branch Management**: Use feature branches from `dev` with naming convention `feature/issue-{number}-{brief-description}`
3. **Development Process**: Follow coding standards, make atomic commits, use task management for complex work
4. **Quality Assurance**: Ensure tests, builds, and linting pass before creating PR
5. **Pull Request Process**: Create PR to `dev` branch with mandatory code review
6. **Completion Criteria**: All quality gates and approvals required before merge

This workflow must be followed for all PatternTrade API development tasks.

## Task Communication Standards

### Task Summary Format

- Always provide a concise, bullet-point summary at the end of task completion
- Limit summaries to 3-5 key points maximum, focusing only on what was accomplished and immediate next steps
- Avoid lengthy explanations in summaries - keep them action-oriented and specific

## Architectural Scope Reduction Notes

### Single Broker Focus (Temporary)

- **Current Scope**: Zerodha Kite Connect integration only
- **Purpose**: Accelerate development velocity and reach live trading faster
- **Architecture**: Direct integration without broker abstraction layers
- **Future Considerations**: Code structure maintained for potential multi-broker expansion

### Development Priorities

1. **Speed over Flexibility**: Prioritize rapid implementation over abstract broker patterns
2. **Kite-Native Approach**: Use Kite Connect library and formats directly
3. **Clean Implementation**: Maintain good code structure for future extensibility
4. **Live Trading Focus**: Optimize for getting to production trading capabilities quickly

### Migration Path for Future Multi-Broker Support

- Current Kite-specific implementations can be wrapped in broker interfaces later
- Symbol management can be extended with mapping layers when needed
- WebSocket handling patterns can be abstracted for multiple brokers
- Configuration patterns support easy addition of new broker configs
