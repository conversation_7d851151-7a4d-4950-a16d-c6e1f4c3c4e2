import { Injectable, Logger, OnModuleD<PERSON>roy } from '@nestjs/common';
import { Queue, Job, JobsOptions, RepeatOptions, JobScheduler } from 'bullmq';
import { DateTimeUtilsService } from '@app/utils';
import { QueueError, type QueueHealthStatus } from './queue.error';
import { type JobDataType, JobDataSchema, QUEUE_PRIORITY_MAP, type QueuePriorityType } from './queue.config';

/**
 * Abstract base class for queue services
 * Provides common queue operations and standardized error handling
 * Now uses @nestjs/bullmq for queue injection
 */
@Injectable()
export abstract class BaseQueueService<T extends Record<string, unknown> = Record<string, unknown>>
  implements OnModuleDestroy
{
  protected readonly logger = new Logger(this.constructor.name);
  protected queue: Queue<T>;

  constructor(
    queue: Queue<T>,
    protected readonly dateTimeUtils: DateTimeUtilsService,
  ) {
    this.queue = queue;
  }

  /**
   * Get queue name for logging and identification
   */
  getQueueName(): string {
    return this.queue.name;
  }

  /**
   * Add a job to the queue
   */
  async addJob(jobName: string, data: T, options?: Partial<JobsOptions>): Promise<Job<T>> {
    try {
      // Validate job data
      this.validateJobData({ data, opts: options || {} });

      // Use type assertion to work around BullMQ v5+ complex typing
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const job = await (this.queue as any).add(jobName, data, options);

      this.logger.log(`Job '${jobName}' added to queue '${this.getQueueName()}' with ID: ${job.id}`);
      return job;
    } catch (error) {
      this.logger.error(`Failed to add job '${jobName}' to queue '${this.getQueueName()}':`, error);
      throw new QueueError('JOB_ADD_FAILED', {
        message: `Failed to add job '${jobName}' to queue '${this.getQueueName()}'`,
        cause: error,
        context: { queueName: this.getQueueName(), jobName, data },
      });
    }
  }

  /**
   * Add a job with priority
   */
  async addJobWithPriority(
    jobName: string,
    data: T,
    priority: QueuePriorityType,
    options?: Partial<JobsOptions>,
  ): Promise<Job<T>> {
    const priorityValue = QUEUE_PRIORITY_MAP[priority];
    return this.addJob(jobName, data, { ...options, priority: priorityValue });
  }

  /**
   * Add a delayed job
   */
  async addDelayedJob(jobName: string, data: T, delayMs: number, options?: Partial<JobsOptions>): Promise<Job<T>> {
    return this.addJob(jobName, data, { ...options, delay: delayMs });
  }

  /**
   * Add a repeating job using the new Job Scheduler API
   * @deprecated Use upsertJobScheduler instead for better performance and reliability
   */
  async addRepeatingJob(
    jobName: string,
    data: T,
    repeatOptions: RepeatOptions,
    options?: Partial<JobsOptions>,
  ): Promise<Job<T>> {
    return this.addJob(jobName, data, { ...options, repeat: repeatOptions });
  }

  /**
   * Create or update a job scheduler (replaces deprecated repeatable jobs)
   * Uses the new BullMQ Job Schedulers API for better performance and reliability
   */
  async upsertJobScheduler(
    schedulerId: string,
    repeatOptions: {
      every?: number;
      pattern?: string;
      tz?: string;
      startDate?: Date;
      endDate?: Date;
      limit?: number;
    },
    jobTemplate: {
      name: string;
      data: T;
      opts?: Partial<JobsOptions>;
    },
  ): Promise<void> {
    try {
      // Validate job data
      this.validateJobData({ data: jobTemplate.data, opts: jobTemplate.opts || {} });

      // Use type assertion to work around BullMQ complex typing
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      await (this.queue as any).upsertJobScheduler(schedulerId, repeatOptions, jobTemplate);

      this.logger.log(
        `Job scheduler '${schedulerId}' upserted for job '${jobTemplate.name}' in queue '${this.getQueueName()}'`,
      );
    } catch (error) {
      this.logger.error(`Failed to upsert job scheduler '${schedulerId}' in queue '${this.getQueueName()}':`, error);
      throw new QueueError('JOB_SCHEDULER_UPSERT_FAILED', {
        message: `Failed to upsert job scheduler '${schedulerId}' in queue '${this.getQueueName()}'`,
        cause: error,
        context: { queueName: this.getQueueName(), schedulerId, jobTemplate },
      });
    }
  }

  /**
   * Remove a job scheduler
   */
  async removeJobScheduler(schedulerId: string): Promise<boolean> {
    try {
      const result = await this.queue.removeJobScheduler(schedulerId);
      this.logger.log(`Job scheduler '${schedulerId}' removed from queue '${this.getQueueName()}'`);
      return result;
    } catch (error) {
      this.logger.error(`Failed to remove job scheduler '${schedulerId}' from queue '${this.getQueueName()}':`, error);
      throw new QueueError('JOB_SCHEDULER_REMOVE_FAILED', {
        message: `Failed to remove job scheduler '${schedulerId}' from queue '${this.getQueueName()}'`,
        cause: error,
        context: { queueName: this.getQueueName(), schedulerId },
      });
    }
  }

  /**
   * Get all job schedulers
   */
  async getJobSchedulers(): Promise<unknown[]> {
    try {
      const schedulers = await this.queue.getJobSchedulers();
      return schedulers as unknown[];
    } catch (error) {
      this.logger.error(`Failed to get job schedulers from queue '${this.getQueueName()}':`, error);
      throw new QueueError('JOB_SCHEDULERS_GET_FAILED', {
        message: `Failed to get job schedulers from queue '${this.getQueueName()}'`,
        cause: error,
        context: { queueName: this.getQueueName() },
      });
    }
  }

  /**
   * Get a job by ID
   */
  async getJob(jobId: string): Promise<Job<T> | undefined> {
    try {
      return await this.queue.getJob(jobId);
    } catch (error) {
      this.logger.error(`Failed to get job '${jobId}' from queue '${this.getQueueName()}':`, error);
      throw new QueueError('JOB_GET_FAILED', {
        message: `Failed to get job '${jobId}' from queue '${this.getQueueName()}'`,
        cause: error,
        context: { queueName: this.getQueueName(), jobId },
      });
    }
  }

  /**
   * Remove a job by ID
   */
  async removeJob(jobId: string): Promise<void> {
    try {
      const job = await this.getJob(jobId);
      if (job) {
        await job.remove();
        this.logger.log(`Job '${jobId}' removed from queue '${this.getQueueName()}'`);
      }
    } catch (error) {
      this.logger.error(`Failed to remove job '${jobId}' from queue '${this.getQueueName()}':`, error);
      throw new QueueError('JOB_REMOVE_FAILED', {
        message: `Failed to remove job '${jobId}' from queue '${this.getQueueName()}'`,
        cause: error,
        context: { queueName: this.getQueueName(), jobId },
      });
    }
  }

  /**
   * Pause the queue
   */
  async pauseQueue(): Promise<void> {
    try {
      await this.queue.pause();
      this.logger.log(`Queue '${this.getQueueName()}' paused`);
    } catch (error) {
      this.logger.error(`Failed to pause queue '${this.getQueueName()}':`, error);
      throw new QueueError('QUEUE_PAUSE_FAILED', {
        message: `Failed to pause queue '${this.getQueueName()}'`,
        cause: error,
        context: { queueName: this.getQueueName() },
      });
    }
  }

  /**
   * Resume the queue
   */
  async resumeQueue(): Promise<void> {
    try {
      await this.queue.resume();
      this.logger.log(`Queue '${this.getQueueName()}' resumed`);
    } catch (error) {
      this.logger.error(`Failed to resume queue '${this.getQueueName()}':`, error);
      throw new QueueError('QUEUE_RESUME_FAILED', {
        message: `Failed to resume queue '${this.getQueueName()}'`,
        cause: error,
        context: { queueName: this.getQueueName() },
      });
    }
  }

  /**
   * Get queue health status
   */
  async getHealthStatus(): Promise<QueueHealthStatus> {
    try {
      const [waiting, active, completed, failed, delayed] = await Promise.all([
        this.queue.getWaiting(),
        this.queue.getActive(),
        this.queue.getCompleted(),
        this.queue.getFailed(),
        this.queue.getDelayed(),
      ]);

      const isPaused = await this.queue.isPaused();

      return {
        name: this.getQueueName(),
        isHealthy: true,
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length,
        delayed: delayed.length,
        paused: isPaused,
      };
    } catch (error) {
      this.logger.error(`Failed to get health status for queue '${this.getQueueName()}':`, error);
      return {
        name: this.getQueueName(),
        isHealthy: false,
        waiting: 0,
        active: 0,
        completed: 0,
        failed: 0,
        delayed: 0,
        paused: false,
        lastError: error instanceof Error ? error.message : 'Unknown error',
        lastErrorAt: this.dateTimeUtils.getUtcNow(),
      };
    }
  }

  /**
   * Clean completed and failed jobs
   */
  async cleanQueue(olderThanMs: number = 24 * 60 * 60 * 1000): Promise<void> {
    try {
      await Promise.all([this.queue.clean(olderThanMs, 100, 'completed'), this.queue.clean(olderThanMs, 50, 'failed')]);
      this.logger.log(`Queue '${this.getQueueName()}' cleaned successfully`);
    } catch (error) {
      this.logger.error(`Failed to clean queue '${this.getQueueName()}':`, error);
      throw new QueueError('QUEUE_CLEAN_FAILED', {
        message: `Failed to clean queue '${this.getQueueName()}'`,
        cause: error,
        context: { queueName: this.getQueueName() },
      });
    }
  }

  /**
   * Validate job data using Zod schema
   */
  private validateJobData(data: unknown): JobDataType {
    try {
      return JobDataSchema.parse(data);
    } catch (error) {
      throw new QueueError('INVALID_JOB_DATA', {
        message: 'Invalid job data provided',
        cause: error,
        context: { queueName: this.getQueueName(), data },
      });
    }
  }

  /**
   * Get the queue instance (for advanced operations)
   */
  getQueue(): Queue<T> {
    return this.queue;
  }

  // ==================== GENERIC JOB MANAGEMENT ====================

  /**
   * Cancel a job by ID with enhanced logging
   */
  async cancelJob(jobId: string): Promise<boolean> {
    try {
      this.logger.log(`Cancelling job '${jobId}' in queue '${this.getQueueName()}'`);

      const job = await this.getJob(jobId);
      if (!job) {
        this.logger.warn(`Job '${jobId}' not found for cancellation in queue '${this.getQueueName()}'`);
        return false;
      }

      await this.removeJob(jobId);

      this.logger.log(`Job '${jobId}' cancelled successfully in queue '${this.getQueueName()}'`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to cancel job '${jobId}' in queue '${this.getQueueName()}':`, error);
      throw new QueueError('JOB_CANCEL_FAILED', {
        message: `Failed to cancel job '${jobId}' in queue '${this.getQueueName()}'`,
        cause: error,
        context: { queueName: this.getQueueName(), jobId },
      });
    }
  }

  /**
   * Cancel all jobs with a specific request ID
   */
  async cancelJobsByRequestId(requestId: string): Promise<number> {
    try {
      this.logger.log(`Cancelling jobs by request ID '${requestId}' in queue '${this.getQueueName()}'`);

      // Get jobs from different states using the queue instance
      const [waitingJobs, activeJobs, delayedJobs] = await Promise.all([
        this.queue.getWaiting(),
        this.queue.getActive(),
        this.queue.getDelayed(),
      ]);
      const jobs = [...waitingJobs, ...activeJobs, ...delayedJobs];
      const matchingJobs = jobs.filter(
        (job) => job.data && 'requestId' in job.data && job.data.requestId === requestId,
      );

      let cancelledCount = 0;
      for (const job of matchingJobs) {
        try {
          if (job.id) {
            await this.removeJob(job.id);
            cancelledCount++;
          }
        } catch (error) {
          this.logger.warn(`Failed to cancel individual job '${job.id}' in queue '${this.getQueueName()}':`, {
            jobId: job.id,
            requestId,
            error: error instanceof Error ? error.message : 'Unknown error',
          });
        }
      }

      this.logger.log(`Jobs cancelled by request ID '${requestId}' in queue '${this.getQueueName()}'`, {
        requestId,
        totalFound: matchingJobs.length,
        cancelled: cancelledCount,
      });

      return cancelledCount;
    } catch (error) {
      this.logger.error(`Failed to cancel jobs by request ID '${requestId}' in queue '${this.getQueueName()}':`, error);
      throw new QueueError('JOBS_CANCEL_BY_REQUEST_ID_FAILED', {
        message: `Failed to cancel jobs by request ID '${requestId}' in queue '${this.getQueueName()}'`,
        cause: error,
        context: { queueName: this.getQueueName(), requestId },
      });
    }
  }

  /**
   * Clean old completed and failed jobs with enhanced options
   */
  async cleanOldJobs(options?: {
    olderThanHours?: number;
    keepCompleted?: number;
    keepFailed?: number;
  }): Promise<{ cleaned: number; kept: number }> {
    try {
      const olderThanHours = options?.olderThanHours || 24;
      const keepCompleted = options?.keepCompleted || 50;
      const keepFailed = options?.keepFailed || 20;

      this.logger.log(`Cleaning old jobs in queue '${this.getQueueName()}'`, {
        olderThanHours,
        keepCompleted,
        keepFailed,
      });

      const olderThanMs = olderThanHours * 60 * 60 * 1000;

      // Clean the queue (base method returns void)
      await this.cleanQueue(olderThanMs);

      // Since we can't get exact count from base cleanQueue method,
      // we'll return the configured keep counts as an approximation
      const estimatedCleaned = Math.max(0, keepCompleted + keepFailed);

      this.logger.log(`Old jobs cleaned successfully in queue '${this.getQueueName()}'`, {
        cleaned: estimatedCleaned,
        olderThanHours,
      });

      return {
        cleaned: estimatedCleaned,
        kept: keepCompleted + keepFailed,
      };
    } catch (error) {
      this.logger.error(`Failed to clean old jobs in queue '${this.getQueueName()}':`, error);
      throw new QueueError('CLEAN_OLD_JOBS_FAILED', {
        message: `Failed to clean old jobs in queue '${this.getQueueName()}'`,
        cause: error,
        context: { queueName: this.getQueueName(), options },
      });
    }
  }

  // ==================== GENERIC JOB STATUS AND MONITORING ====================

  /**
   * Get enhanced job status with detailed information
   */
  async getJobStatus(jobId: string): Promise<{
    id: string;
    name?: string;
    data?: T;
    progress: number;
    state: string;
    attempts: number;
    maxAttempts: number;
    createdAt?: Date;
    processedAt?: Date;
    finishedAt?: Date;
    failedReason?: string;
    result?: unknown;
    timestamp: Date;
  } | null> {
    try {
      const job = await this.getJob(jobId);
      if (!job) {
        return null;
      }

      const state = await job.getState();

      return {
        id: job.id || 'unknown',
        name: job.name,
        data: job.data,
        progress: typeof job.progress === 'number' ? job.progress : 0,
        state,
        attempts: job.attemptsMade,
        maxAttempts: job.opts?.attempts || 3,
        createdAt: job.timestamp ? new Date(job.timestamp) : undefined,
        processedAt: job.processedOn ? new Date(job.processedOn) : undefined,
        finishedAt: job.finishedOn ? new Date(job.finishedOn) : undefined,
        failedReason: job.failedReason,
        result: job.returnvalue,
        timestamp: this.dateTimeUtils.getNewDate(),
      };
    } catch (error) {
      this.logger.error(`Failed to get job status for '${jobId}' in queue '${this.getQueueName()}':`, error);
      return null;
    }
  }

  /**
   * Get multiple job statuses by request ID
   */
  async getJobStatusByRequestId(requestId: string): Promise<
    Array<{
      id: string;
      name?: string;
      data?: T;
      progress: number;
      state: string;
      attempts: number;
      timestamp: Date;
    }>
  > {
    try {
      // Get all jobs from different states and filter by request ID
      const [waitingJobs, activeJobs, completedJobs, failedJobs, delayedJobs] = await Promise.all([
        this.queue.getWaiting(),
        this.queue.getActive(),
        this.queue.getCompleted(),
        this.queue.getFailed(),
        this.queue.getDelayed(),
      ]);
      const jobs = [...waitingJobs, ...activeJobs, ...completedJobs, ...failedJobs, ...delayedJobs];
      const matchingJobs = jobs.filter(
        (job) => job.data && 'requestId' in job.data && job.data.requestId === requestId,
      );

      const statuses = await Promise.all(
        matchingJobs.map(async (job) => {
          const state = await job.getState();
          return {
            id: job.id || 'unknown',
            name: job.name,
            data: job.data as T,
            progress: typeof job.progress === 'number' ? job.progress : 0,
            state: state as string,
            attempts: job.attemptsMade,
            timestamp: new Date(),
          };
        }),
      );

      return statuses;
    } catch (error) {
      this.logger.error(
        `Failed to get job status by request ID '${requestId}' in queue '${this.getQueueName()}':`,
        error,
      );
      return [];
    }
  }

  /**
   * Get pending jobs count
   */
  async getPendingJobsCount(): Promise<number> {
    const healthStatus = await this.getHealthStatus();
    return healthStatus.waiting + healthStatus.delayed;
  }

  /**
   * Get active jobs count
   */
  async getActiveJobsCount(): Promise<number> {
    const healthStatus = await this.getHealthStatus();
    return healthStatus.active;
  }

  /**
   * Cleanup resources on module destroy
   */
  async onModuleDestroy(): Promise<void> {
    try {
      await this.queue.close();
      this.logger.log(`Queue '${this.getQueueName()}' closed successfully`);
    } catch (error) {
      this.logger.error(`Error closing queue '${this.getQueueName()}':`, error);
    }
  }
}
