import { EnvironmentEnum } from '@app/common/constants';
import { z } from 'zod/v4';

// Core environment variables (sensitive/critical - from env vars)
export const EnvSchema = z.object({
  // Application
  NODE_ENV: EnvironmentEnum.default('development'),
  PORT: z.coerce.number().int().min(1).max(65535).default(3000),

  // Database (sensitive)
  DB_HOST: z.string().min(1),
  DB_PORT: z.coerce.number().int().min(1).max(65535).default(5432),
  DB_NAME: z.string().min(1),
  DB_USERNAME: z.string().min(1),
  DB_PASSWORD: z.string().min(1),
  DB_SSL: z
    .enum(['true', 'false'])
    .default('false')
    .transform((val) => val === 'true'),

  // Redis (sensitive)
  REDIS_HOST: z.string().min(1),
  REDIS_PORT: z.coerce.number().int().min(1).max(65535).default(6379),
  REDIS_USERNAME: z.string().optional(),
  REDIS_PASSWORD: z.string().optional(),
  REDIS_URL: z.url().optional(),

  // QuestDB (sensitive)
  QUESTDB_HOST: z.string().min(1).default('localhost'),
  QUESTDB_PORT: z.coerce.number().int().min(1).max(65535).default(8812),
  QUESTDB_ILP_PORT: z.coerce.number().int().min(1).max(65535).default(9009), // InfluxDB Line Protocol port for bulk inserts
  QUESTDB_USERNAME: z.string().optional(),
  QUESTDB_PASSWORD: z.string().optional(),
  QUESTDB_DATABASE: z.string().min(1).default('qdb'),
  QUESTDB_SSL: z
    .enum(['true', 'false'])
    .default('false')
    .transform((val) => val === 'true'),

  // API Keys (sensitive)
  JWT_SECRET: z.string().min(32),
  SESSION_SECRET_KEY: z.string().min(32),
  ANTHROPIC_API_KEY: z.string().startsWith('sk-').optional(),
  OPENAI_API_KEY: z.string().startsWith('sk-').optional(),

  // Broker Management (sensitive)
  BROKER_ENCRYPTION_KEY: z.string().min(32),
  BROKER_ENCRYPTION_SALT: z.string().min(16).optional(),

  // IP Whitelisting (optional - loaded via getArbitrary)
  // IP_WHITELIST_ENABLED: z.enum(['true', 'false']).default('false').transform((val) => val === 'true').optional(),
  // ALLOWED_IPS: z.string().optional(),
  // ALLOWED_CIDRS: z.string().optional(),
  // ALLOW_PRIVATE_IPS: z.enum(['true', 'false']).default('true').transform((val) => val === 'true').optional(),
  // ALLOW_LOOPBACK: z.enum(['true', 'false']).default('true').transform((val) => val === 'true').optional(),
});

// Export types following PatternTrade API standards
export type EnvType = z.output<typeof EnvSchema>;
