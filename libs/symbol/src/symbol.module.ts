import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bullmq';
import { QueueNameEnum } from '@app/core/queue';
import { SymbolService } from './symbol.service';
import { SymbolDownloadQueueService } from './symbol.queue';
import { SymbolDownloadWorkerService } from '../../../apps/datastore/src/symbol/symbol.worker';
import { SymbolRepository } from './symbol.questdb-repository';

/**
 * Symbol Module
 *
 * Provides comprehensive symbol master data management functionality including:
 * - Symbol data storage and retrieval with QuestDB
 * - Background job processing with BullMQ
 * - Audit logging with PostgreSQL
 * - redis-style microservice communication
 * - Real-time data synchronization with Kite Connect API
 *
 * Features:
 * - Symbol master data repository
 * - Download queue and worker services
 * - Audit logging and compliance
 * - Microservice communication
 * - Health monitoring and statistics
 */
@Module({
  imports: [
    BullModule.registerQueue({
      name: QueueNameEnum.enum.SYMBOL_DOWNLOAD,
    }),
  ],
  providers: [
    // Core services
    SymbolService,
    SymbolRepository,

    // Queue and worker services
    SymbolDownloadQueueService,
    SymbolDownloadWorkerService,
  ],
  exports: [
    // Core services
    SymbolService,
    SymbolRepository,

    // Queue and worker services
    SymbolDownloadQueueService,
    SymbolDownloadWorkerService,
  ],
})
export class SymbolModule {}
