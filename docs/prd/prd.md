# Pattern Trade (PT) Project Documentation

## 1. Product Requirements Document (PRD)

### 1.1 Introduction

**Project Name:** Pattern Trade (PT)

**Vision & Goal:**
The primary vision of Pattern Trade (PT) is to serve as a **Personal Finance Assistant**, specifically designed to empower middle-class individuals by providing an **Algo Trading Platform**. The core objective is to help users autonomously grow their income through systematic trading, addressing the limitations and lack of customizability often found in existing market applications.

**Problem Statement:**
Many current market trading applications lack the flexibility and specific features required for personalized algorithmic trading strategies. They often come with predefined functionalities that cannot be easily customized to suit individual user needs or adapt to unique market conditions. Furthermore, there's a significant gap in accessible tools for the average individual to effectively leverage algorithmic trading for income growth. PT aims to bridge this gap by offering a robust, customizable, and user-friendly platform that automates trading decisions and execution, thereby maximizing potential returns for its users.

### 1.2 Scope

**In-Scope Functionalities:**

- Automated Algorithmic Trading Execution
- Multi-Broker Integration and Management
- Comprehensive Real-time and Historical Data Handling
- Advanced Strategy Definition, Backtesting, and Optimization
- Robust Order Management and Monitoring
- Detailed Reporting, Analytics, and Observability
- Secure User Authentication and Role-Based Authorization
- Support for multiple asset classes and trading styles.

**Out-of-Scope (Initial Phase):**

- Direct user self-registration/sign-up (users will be manually onboarded by admins initially).
- Advanced UI automation for broker logins (manual intervention for certain brokers may be required in the initial phase, though automated solutions are planned).

### 1.3 User Personas

**1. Admin (PT Owners/Super Admin/System Admin):**

- **Role:** Oversees the entire PT system, manages broker integrations, ensures data integrity, defines and optimizes strategies, monitors system health, and manages user access.
- **Key Needs:** Comprehensive control, full visibility into system operations, ability to configure and switch data sources (brokers), and access to powerful backtesting and analytics tools.

**2. Normal User:**

- **Role:** Utilizes the PT platform to execute automated trading strategies on their linked broker accounts. Primarily interested in passive income generation and monitoring their trades and portfolio performance.
- **Key Needs:** Easy access to automated trading, ability to select pre-defined or recommended strategies, clear view of trading activity and performance, and notifications regarding critical account or trade events.

### 1.4 Key Features (High-Level)

- **Automated Trading:** Enables the system to autonomously execute trades based on predefined strategies.
- **Multi-Broker Support:** Seamless integration with various major stockbrokers to diversify operations and mitigate risks associated with single-broker reliance.
- **Comprehensive Data Handling:** Efficient acquisition, processing, and storage of real-time tick data, historical market data, symbol universe data, and fundamental company data.
- **Advanced Strategy Execution:** Tools for defining complex trading strategies, performing rigorous backtesting, and optimizing performance across different market conditions and asset classes.
- **Robust Monitoring & Analytics:** Real-time tracking of trades, orders, system health, and detailed performance reporting through an integrated observability stack.
- **Seamless User Experience:** A user-friendly interface (initially web-based) for managing accounts, selecting strategies, and viewing trading outcomes.

### 1.5 Functional Requirements

- **1.5.1 Data Acquisition:**
  - **Symbol Universe:** System must connect to multiple brokers to download and maintain an up-to-date "Symbol Universe" (list of all tradable symbols).
  - **Tick Data:** Real-time per-second tick data must be acquired from brokers via WebSockets for all supported symbols.
  - **Fundamental Data:** The system must acquire fundamental company data (e.g., market cap, P/E ratio, outstanding shares) via web scraping from public financial websites. This data is critical for advanced screening.
- **1.5.2 Data Transformation & Storage:**
  - **1-Minute Candle Generation:** The system must process raw per-second tick data, clubbing it into 1-minute candle data for strategy execution and historical storage.
  - **Common Symbol Format:** All symbol data from different brokers must be converted and stored in a standardized, common format within PT to ensure interoperability and consistent processing.
  - **Historical Data Storage:** All collected and transformed data (Symbol Universe, 1-minute candles, fundamental data) must be persistently stored for backtesting and analysis.
- **1.5.3 Strategy Management:**
  - **Screener:** A powerful Screener module must allow admins to define criteria (based on technical and fundamental data) to filter the Symbol Universe and generate specific Watchlists.
  - **Watchlist:** The system must support the creation and management of watchlists derived from the Screener module or pre-defined lists (e.g., Nifty 50, Nifty 200).
  - **Strategy Definition:** Admins must be able to define and configure various trading strategies (e.g., based on technical indicators like RSI, SMA).
  - **Strategy Assignment:** Strategies must be assignable to specific watchlists and individual broker accounts.
- **1.5.4 Trade Execution Flow:**
  - **Signal Generation:** Strategies, when executed, must generate "Buy" or "Sell" signals.
  - **Order Conversion:** Signals must be converted into executable orders (e.g., market orders, limit orders).
  - **Order Routing:** Orders must be routed to the appropriate user's linked broker account for execution.
- **1.5.5 Order Management & Monitoring:**
  - **Order Status Tracking:** The system must continuously monitor the status of open orders (e.g., open, partially filled, filled, cancelled).
  - **Exit Condition Monitoring:** Automated monitoring for target price, stop-loss (including trailing stop-loss), and other exit conditions to trigger order closure.
  - **Order Lifecycle Management:** Management of orders from creation through execution and closure.
  - **Advanced Order Types:** Support for advanced order types such as order slicing (dividing large orders into smaller parts for discreet execution), idempotency (ensuring orders are processed only once), parallel triggering, and copy trading.
- **1.5.6 Reporting & Analytics:**
  - **Journal Entry:** Comprehensive logging of all trading activities, including order creation, execution details, and P&L, for auditing and analysis.
  - **Backtest Engine:** A robust backtesting engine capable of running strategies against historical data, supporting various strategy versions and parameter sets to identify optimal performance.
  - **Performance Metrics:** Calculation and display of key performance indicators for strategies and overall portfolio.
- **1.5.7 User Authentication & Authorization:**
  - **Login Only:** Users can only log in to the system; direct self-registration/sign-up is not supported. User accounts are created and managed by admins.
  - **Role-Based Access Control (RBAC):** Implementation of RBAC to define and enforce user permissions based on their assigned roles (Admin, Normal User).
- **1.5.8 Broker Management:**
  - **Broker Account Onboarding:** Ability for both admins and users to onboard (add) their respective broker accounts to PT by providing necessary credentials.
  - **Primary Data Broker Selection:** Admin can select a primary broker for data acquisition.
  - **Broker Login Status Monitoring:** Continuous monitoring of broker login status, with automated re-login attempts for most brokers and notifications for users if manual intervention is required for certain brokers (e.g., Zerodha).

### 1.6 Future Considerations (Phased Approach)

PT is designed for a phased rollout to ensure stability and continuous improvement.

- **Initial Trading Style Focus:** The first phase will primarily focus on Positional/Swing Trading, with Intraday trading (including Expiry Day strategies) to be introduced in subsequent phases. Long-term investment support will also be part of later phases.
- **Asset Class Expansion:** While the architecture supports all asset classes, the initial focus may be on Equity, followed by Derivatives (F&O: Futures, Options), Currency, and Commodities in later stages.

## 2. Technical Aspects

### 2.1 Architecture Overview

**Pattern Trade (PT)** will be built on a **Microservice Architecture**, providing high scalability, resilience, and maintainability. This approach allows independent development, deployment, and scaling of individual services, contributing to overall system robustness and performance. The system is fundamentally divided into a Frontend (UI), a powerful Backend Processing System, and multiple specialized Databases.

The backend system is further categorized into a set of independent **Applications** and reusable **Libraries**, reflecting a monorepo structure.

**Core Components:**

- **API Gateway:** The central entry point for all UI and external communications, responsible for routing requests and managing connections.
- **Backend System (Microservices):** A collection of loosely coupled services that handle specific business functionalities, acting as the "processing engine" for trading operations.
- **Databases:** A multi-database strategy will be employed, leveraging the strengths of different database technologies for specific data types and access patterns.

### 2.2 Technology Stack

- **Backend:**
  - **Technology:** Node.js
  - **Framework:** NestJS (A progressive Node.js framework for building efficient, reliable, and scalable server-side applications. Used for backend services/API routes within a mono-repo structure, enabling modular architecture, dependency injection, and robust microservice support.)
  - **Architecture Pattern:** Microservice (enabling independent scaling and development).
- **Frontend:**
  - **Technology:** React (a JavaScript library for building user interfaces).
  - **Framework:** Vite (a fast frontend build tool that provides a lean and opinionated development experience for modern web projects).
  - **Target (Initial):** Web Application (with potential for mobile in the future).
- **API Gateway (Internal/External Communication):**
  - **Protocols:** HTTP, HTTPS (for external UI and communications), WebSocket (for real-time tick data acquisition from brokers).
  - **Inter-service Communication:** Redis transport (for all microservice-to-microservice communication, both synchronous and asynchronous).
- **Inter-service Communication (Other Microservices):** All microservices/applications communicate using Redis transport for both synchronous calls and asynchronous event-driven communication.
- **Databases:**
  - **PostgreSQL:** Used for general-purpose relational data, master data (excluding time-series), and journal entries due to its robustness, ACID compliance, scalability, and suitability for financial applications. Drizzle ORM is used for database interactions.
  - **QuestDB:** A high-performance, open-source time-series database specifically chosen for storing market data (tick data, 1-minute candle data). It excels in high-volume ingestion and low-latency queries, crucial for trading platforms.
  - **Redis:** Utilized for various purposes, including caching, messaging queues, and real-time data structures. It's used as the transport layer for asynchronous events and for BullMQ jobs.
  - **DuckDB:** An in-process SQL OLAP database used for quick, local analytics and potentially for temporary storage of processed data.
  - **Firestore:** (Mentioned as part of Core Library, potential use for specific data sets not covered by primary DBs).
- **Queueing System:**
  - **BullMQ:** A robust queueing system (built on Redis) for managing background jobs and scheduled tasks.
  - **Dragonfly:** A Redis-compatible in-memory data store service used to power BullMQ, ensuring high performance for job queuing.
- **State Management/Workflow Engine:**
  - **XState:** A library for building and interpreting state machines and statecharts, used to ensure server resilience and the ability to resume workflows from where they left off after a crash.
- **Observability & Monitoring:**
  - **Grafana:** For creating dashboards and visualizing system metrics, logs, and traces, providing "complete visibility" into system operations.
  - **Logging & Metrics:** Comprehensive collection of logs (using Pino Logger), metrics, and traces across all microservices for debugging, performance analysis, and auditing.
- **Authentication & Authorization:**
  - **AWS Cognito:** Managed service for user authentication (sign-in only, no direct sign-up) and user directory management.
  - **Role-Based Access Control (RBAC):** Implemented for fine-grained authorization, assigning specific roles (e.g., Admin, Normal User) with corresponding privileges.

### 2.3 Data Flow

1.  **Data Ingestion:**
    - **Symbol Universe:** Downloaded periodically from various Broker APIs (e.g., Zerodha, Alice Blue).
    - **Tick Data:** Real-time streaming via WebSockets from Broker APIs.
    - **Fundamental Data:** Scraped from external financial websites.
2.  **Data Transformation & Storage (Symbol Universe Module, Ticker Module, Screener Module):**
    - Raw Symbol Universe data from different brokers is converted into a **common format** within PT.
    - Per-second Tick Data is aggregated into **1-minute candle data**.
    - Fundamental data is processed and stored.
    - All processed data is stored in **QuestDB** (time-series) and **PostgreSQL** (master data/configurations).
3.  **Strategy Execution Workflow:**
    - **Scheduler Service:** Triggers jobs at predefined intervals (e.g., download Symbol Universe at 8 AM, start streaming at 9:15 AM). These jobs are managed via BullMQ.
    - **Screener Module:** Filters Symbol Universe and Tick Data (along with Fundamental Data) based on defined criteria to generate Watchlists.
    - **Strategy Module:** Strategies are applied to Watchlists.
    - **Analyzer Module:** Executes strategies against real-time 1-minute candle data and generates Buy/Sell Signals.
    - **Signal Module:** Processes generated signals for further action.
4.  **Order Management & Execution (Monitor Service, Order Module, Journal Module):**
    - **Monitoring Service:** Receives signals, evaluates order conditions (e.g., existing open orders, target/stop-loss, exit conditions), and coordinates with the Order Module. It maintains full visibility of all orders.
    - **Order Module:** Creates and manages orders based on signals from the Monitor Service, handles advanced order types, and interacts with Broker APIs for order placement and cancellation.
    - **Journal Module:** Records all order-related events and financial transactions for auditing and reporting.
5.  **Backtesting (Backtest Engine):**
    - Historical data from QuestDB/PostgreSQL is fed into the Backtest Engine.
    - The engine runs strategies (including different versions and parameters) against this historical data to evaluate performance and identify optimal configurations.
    - Top-performing strategies are identified and promoted for live trading.

### 2.4 Key Modules & Services

Pattern Trade's backend system is structured around independent **Applications** (microservices) and reusable **Libraries** that encapsulate common logic.

**Applications (Top-Level Microservices):**
These are distinct deployable units that handle specific high-level functionalities within the PT system. Each application typically comprises various internal modules.

- **API Gateway:** Serves as the central entry point for all external communication (UI, Brokers), routing requests to appropriate backend services.
- **Master Data / Data Store Application:** Central repository for Symbol Universe, Fundamental Data, and system configurations. Supports CRUD operations.
- **Ticker Application:** Handles real-time per-second tick data ingestion from brokers via WebSockets and aggregates it into 1-minute OHLCV (Open, High, Low, Close, Volume) candle data, storing them in QuestDB.
- **Order Module (OMS - Order Management System) Application:** Responsible for creating, modifying, and cancelling orders. Supports advanced features like order slicing, idempotency, parallel triggers, and copy trading. Integrates directly with broker APIs for order placement.
- **Monitoring Application:** A critical component that continuously tracks the status of all open orders, monitors for profit targets, stop-losses (including trailing stop-loss), and other exit conditions, ensuring timely closure of trades. Requires high performance and full visibility.
- **Simulator Application:** Provides a paper trading environment, enabling simulated trading using live market data without involving real money. Includes journal entry for tracking simulated trades.
- **Screener Application:** Applies predefined criteria (based on fundamental and technical indicators) to the Symbol Universe and 1-minute candle data to generate targeted Watchlists. Considered a continuous background job/application.
- **Analyzer Application:** The core component that executes configured strategies on real-time market data to generate trading signals (Buy/Sell). This application is responsible for running all strategies.

**Libraries:**
Libraries encapsulate core business logic and reusable functionalities, shared across various applications within the monorepo. When referred to as a "module" in the context of libraries, it signifies a library.

- **User Library:** Contains core business logic for managing user profiles and other user-related information in the system.
- **Auth Library:** Handles both authentication and authorization logic, including Role-Based Access Control (RBAC).
- **Admin Library:** Contains business logic for administrator functionalities.
- **Broker Library:** Provides core logic and functionalities for integrating with and managing various broker APIs, including account onboarding and login status monitoring.
- **Ticker Library:** Contains the core logic for processing and transforming raw tick data into usable formats, distinct from the Ticker Application which handles the overall ingestion and storage.
- **Strategy Library:** Manages the definition, configuration, and application of trading strategies. Also provides the core functionality for running trading strategies against historical data, evaluating their performance, and optimizing parameters (Backtest Engine).
- **Watchlist Library:** Handles the creation, modification, and management of watchlists.
- **Symbol Library:** Manages the acquisition, standardization, and maintenance of the symbol universe.
- **Signal Library:** Contains the core logic for processing raw trading signals from the Analyzer and ensuring they meet pre-execution checks.
- **Monitor Library:** Provides core monitoring logic and functionalities related to order tracking and exit condition monitoring.
- **Stats Library:** Responsible for collecting statistics and metrics throughout the application.
- **Benchmark Library:** Responsible for tracking various performance and operational numbers within the system.
- **Fund Library:** Handles the management and tracking of user funds within integrated broker accounts.
- **Positions Library:** Manages the tracking of open and closed trading positions.
- **Holdings Library:** Responsible for managing and tracking long-term portfolio holdings.
- **Tradebook Library:** Handles the recording and management of all trades executed for orders.
- **Dashboard Library:** Provides core logic for aggregating and displaying data in a unified dashboard view.
- **Journal Library:** Contains the core logic for detailed logging and record-keeping of all trading activities and financial transactions.
- **Audit Log Library:** Manages the logging of all user actions and system events for auditing and compliance.
- **Notification Library:** Handles the generation and dispatch of various system and user-specific notifications.
- **Scanner Library:** Contains the core logic for continuously processing the Symbol Universe and Tick Data, combined with Fundamental and Technical analysis, to identify trading opportunities.
- **Data Recorder Library:** Provides core logic for end-of-day processes to consolidate and append daily collected data into long-term historical archives.
- **Scheduler Library:** Encapsulates core logic related to scheduling and triggering automated tasks and managing job queues.
- **Core Library:** Houses fundamental, cross-cutting concerns and shared utilities for the entire system.
  - **ENV Module:** For managing environment variables.
  - **Firestore Module:** Potentially for specific data storage or integration with Google Firestore.
  - **DB Module (Drizzle ORM):** Provides database interaction functionalities, utilizing Drizzle ORM.
  - **Cache Module:** Implements caching mechanisms for performance optimization.
  - **Queue Module:** Contains logic for managing background jobs and queues.
  - **Health Check Module:** Provides functionalities for monitoring the health of system components.
  - **Pino Logger Module:** Integrates Pino as the logging library for structured logging.
  - **Transport Module:** Handles various communication transport layers between services.
  - **QuestDB:** An internal abstraction or helper module for efficient time-series database operations within the Core.
- **Facilitator Library:** Contains modules for facilitating inter-service communication and session management.
  - **Client Module:** Provides client implementations for interacting with other microservices.
  - **Session Module:** Manages user sessions and related data.
- **Socket Library:** Provides functionalities for WebSocket communication.
- **Utility Library:** Offers a collection of general-purpose utility functions and helper modules.
  - **DateTime Module:** For handling date and time manipulations.
  - **Date Module:** Specific utilities for date operations.
  - Other general helper modules.
- **Shared Library:** Contains common, widely-used definitions and configurations across the monorepo.
  - **Constants:** Global constants and magic values.
  - **DB Schema:** Database schema definitions.
  - **Zod Schema:** Validation schemas defined using Zod.
  - **Errors:** Centralized error definitions and handling.

### 2.5 Non-Functional Requirements

- **Robustness:** The system must be highly reliable, resilient to failures, and prevent any module from getting "stuck." Critical components (especially pre-order modules) must be exceptionally robust.
- **Scalability:** The microservice architecture enables independent scaling of individual services. This allows the system to handle a large number of users, symbols, and high transaction volumes efficiently.
- **Resilience:** The system must be able to gracefully recover from server crashes and failures. Utilizing XState, it should resume operations from the exact point of interruption without data loss.
- **High Performance:** Critical operations, especially tick data processing, signal generation, and order management, must execute with ultra-low latency and high throughput. The decision-making process for trades must be extremely fast.
- **Security:** Authentication is handled by AWS Cognito, and authorization uses RBAC, ensuring secure access and operations. Only authorized users can interact with the system.
- **Visibility (Observability):** Comprehensive observability through integrated logging (using Pino Logger), metrics, and tracing, visualized via Grafana. This provides deep insights into system behavior, performance, and any anomalies. Health checks and benchmarking systems will also be in place.
- **Data Integrity:** All financial data and transaction logs must maintain high integrity (e.g., ACID compliance for PostgreSQL) to ensure accuracy and facilitate auditing.
- **Customizability:** The platform is designed to be highly customizable, allowing admins to define and adapt strategies, screeners, and broker integrations as needed.

### 2.6 Challenges & Solutions

- **Broker API Variability:**
  - **Challenge:** Different brokers have diverse API structures, data formats, and symbol naming conventions.
  - **Solution:** Implementation of a "Common Format" utility within the Symbol Universe module to standardize all incoming broker data, ensuring seamless integration and processing across PT.
- **Broker Downtime/Failure:**
  - **Challenge:** Individual brokers can experience technical issues or downtime, posing a risk to continuous trading.
  - **Solution:** Multi-broker support allows for strategy diversification and automatic switching to alternative brokers if a primary broker becomes unavailable.
- **High Volume Tick Data Processing:**
  - **Challenge:** Processing vast amounts of real-time per-second tick data and converting it into usable 1-minute candles efficiently.
  - **Solution:** The Ticker Module is specifically designed for high-throughput ingestion and transformation, leveraging QuestDB's time-series capabilities.
- **Fundamental Data Acquisition:**
  - **Challenge:** Fundamental data is often not available via APIs and may require complex extraction from websites.
  - **Solution:** Development of a web scraping component to reliably collect fundamental data from various public sources.
- **Complex Workflow Management & Resilience:**
  - **Challenge:** Managing intricate trading workflows across multiple microservices and ensuring system state is maintained during crashes.
  - **Solution:** Adoption of XState as a state machine library to define and manage complex workflows, allowing the system to resume operations precisely from the last known state after a server restart.
- **Order Negation/Conflict:**
  - **Challenge:** When multiple strategies are active, or if a strategy issues both buy and sell signals for the same asset within the same broker account, it can lead to conflicts or ineffective trades.
  - **Solution:** Strategies for a specific symbol/side will be directed to different broker accounts or managed to prevent simultaneous conflicting orders within the same account.

## 3. Detailed Features

### 3.1 Core Trading Features

- **Algorithmic Trading Execution:** Automated execution of trades based on predefined rules and strategies.
- **Multi-Broker Integration:**
  - Support for integration with multiple leading stockbrokers (e.g., Zerodha, Dhan, Alice Blue, Finvasia, Angel One).
  - Diversification of trading strategies across different broker accounts to mitigate risk.
  - Admin ability to switch primary data broker in case of service interruptions.
- **Live Trading:** Execution of strategies with real money in actual market conditions.
- **Paper Trading:** Simulated trading using live market data without involving real money, primarily for strategy testing and validation; includes journal entry for tracking.
- **Automated Order Placement & Management:** System-driven creation, modification, and cancellation of trading orders.
- **Support for Various Order Types:**
  - Market Orders
  - Limit Orders
  - **Trailing Stop Loss:** An advanced order type that automatically adjusts the stop price as the market price moves favorably, securing profits while limiting potential losses.
  - Other advanced order types as required.
- **Order Slicing:** Division of large orders into smaller, manageable parts for discreet and efficient execution, minimizing market impact.
- **Idempotent Order Handling:** Ensures that a trading order is processed exactly once, preventing duplicate or erroneous executions.
- **Parallel Order Triggering:** Ability to trigger multiple orders concurrently across different instruments or broker accounts.
- **Copy Trading:** Functionality to replicate trades from one account or strategy to another.

### 3.2 Strategy & Analysis Features

- **Strategy Execution Engine:** The core engine that runs algorithmic trading strategies.
- **Strategy Management & Configuration:**
  - Tools for defining, configuring, and managing diverse trading strategies.
  - Ability to set strategy parameters and conditions.
- **Screener for Watchlist Generation:**
  - Sophisticated filtering mechanism to analyze the entire Symbol Universe.
  - Generates custom watchlists based on user-defined technical (e.g., RSI, SMA) and fundamental (e.g., market cap, P/E ratio) criteria.
  - Supports various screening criteria (e.g., large-cap, mid-cap, small-cap, momentum stocks).
- **Watchlist Management:** Creation, modification, and assignment of watchlists to strategies.
- **Backtesting Engine:**
  - Runs trading strategies against extensive historical market data.
  - Supports backtesting of different strategy versions and parameter combinations.
  - Evaluates strategy performance to identify the most effective configurations.
  - Operates continuously in the background, providing daily and weekly performance reports.
- **Automated Strategy Performance Evaluation:** Identifies top-performing strategies and watchlists from backtesting for live deployment.
- **Support for Diverse Strategy Types:**
  - **Intraday Trading:** Strategies designed for trading within a single market day.
  - **Positional/Swing Trading:** Strategies for capturing price swings over days or weeks.
  - **Long Term Investment:** Strategies focused on long-term capital appreciation.
  - **Expiry Day Trading:** Specialized strategies for options/futures expiry days.

### 3.3 Data Management & Processing

- **Real-time Tick Data Acquisition:** Captures per-second market data streams via WebSockets from integrated brokers.
- **1-Second to 1-Minute Candle Data Aggregation:** Processes raw tick data to generate standardized 1-minute OHLCV (Open, High, Low, Close, Volume) candle data.
- **Symbol Universe Management:** Maintains a comprehensive and updated list of all tradable instruments from various brokers.
- **Fundamental Data Acquisition:** Collects key fundamental company data (e.g., market capitalization, P/E ratio, outstanding shares) through web scraping.
- **Common Symbol Format Conversion:** Converts disparate symbol naming conventions from different brokers into a single, unified format for internal processing.
- **Historical Data Storage:** Persistent storage of all raw and processed market data, fundamental data, and configurations for analysis and backtesting.
- **Multi-Asset Class Support:**
  - **Equity:** Trading in company stocks.
  - **Derivatives:** Futures & Options (F&O), including Stock Options and Index F&O.
  - **Currency:** Trading in foreign exchange.
  - **Commodity:** Trading in raw materials and agricultural products.

### 3.4 System & Operational Features

- **Microservice Architecture:** Modular system design enabling independent development, deployment, and scaling of services.
- **Robust & Scalable Backend:** Designed for high availability, fault tolerance, and ability to handle increasing loads.
- **Server Resilience:** Ability for the system to recover from crashes and resume operations from the last known stable state (using XState).
- **Scheduler for Automated Tasks:**
  - Automates time-based tasks (e.g., daily symbol universe download, market open/close streaming start/stop).
  - Manages job queues using BullMQ/Dragonfly.
- **Centralized Logging, Metrics & Tracing (Observability):**
  - Comprehensive collection of system logs (using Pino Logger), performance metrics, and transaction traces.
  - Visualization through Grafana for real-time monitoring and debugging.
  - Provides complete visibility into internal system processes.
- **Performance Benchmarking:** Tools to measure and ensure optimal system performance.
- **Health Check Monitoring:** Continuous monitoring of system components to ensure they are operational.
- **Audit Logging & Event Logging:** Detailed logs of all user actions and system events for security and compliance.
- **Journal Entry for all Transactions:** Maintains a chronological record of all trading and financial activities.
- **Kill Switch:** An emergency feature allowing immediate cessation of all new trading orders, crucial during unforeseen market events or system failures.
- **Admin Dashboard:** A dedicated interface for administrators to manage configurations, monitor system health, and oversee overall operations.
- **Data Recorder:** An End-of-Day (EOD) process to consolidate and append daily collected data into long-term historical archives.

### 3.5 User & Access Management

- **User Login:** Secure authentication process for users to access the platform.
  - **No Sign-up:** User accounts are not self-registered but are provisioned and managed by system administrators.
  - Utilizes **AWS Cognito** for robust authentication.
- **Role-Based Access Control (RBAC):** Assigns specific permissions and access levels based on user roles (Admin, Normal User).
- **Broker Account Onboarding:** Allows both admins and normal users to link their respective broker accounts to the PT platform.
- **Broker Login Status Monitoring & Re-login Attempts:** Automatically monitors the login status of linked broker accounts and attempts programmatic re-logins when necessary.
- **Notifications for Broker Status:** Alerts users and/or admins when a broker account logs out or encounters an issue requiring manual intervention.
- **Ability for Admin to set Primary Data Broker:** Admins can designate a preferred broker for primary data acquisition.
