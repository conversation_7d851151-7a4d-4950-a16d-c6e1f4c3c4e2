import { Module } from '@nestjs/common';
import { DatastoreService } from './datastore.service';

// Core modules
import { CoreModule } from '@app/core';
import { CommonModule } from '@app/common';
import { UtilsModule } from '@app/utils';

// Symbol module
import { SymbolModule } from '@app/symbol';

/**
 * Datastore Microservice Module
 *
 * Provides data storage and retrieval services for the PatternTrade API ecosystem
 * through microservice communication only (no HTTP endpoints).
 * Includes symbol master data management, audit logging, and dual transport communication.
 *
 * Features:
 * - Symbol master data storage and retrieval
 * - QuestDB integration for time-series data
 * - PostgreSQL integration for audit logs
 * - transport (Redis) for microservice communication
 * - Comprehensive health checks
 * - Background job processing with BullMQ
 * - Microservice-only architecture (no REST endpoints)
 */
@Module({
  imports: [
    // Core infrastructure modules
    CoreModule,
    CommonModule,
    UtilsModule,

    // Business logic modules
    SymbolModule,
  ],
  controllers: [],
  providers: [DatastoreService],
  exports: [DatastoreService],
})
export class DatastoreModule {}
